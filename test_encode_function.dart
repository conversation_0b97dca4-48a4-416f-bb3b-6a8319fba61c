import 'dart:io';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';

void main() {
  group('encodeFileToBase64 Tests', () {
    test('should handle empty file path', () async {
      expect(
        () => SyncUtils.encodeFileToBase64(''),
        throwsArgumentError,
      );
    });

    test('should handle non-existent file path', () async {
      expect(
        () => SyncUtils.encodeFileToBase64('/non/existent/file.txt'),
        throwsA(isA<FileSystemException>()),
      );
    });

    test('should handle valid file path', () async {
      // Create a temporary file
      final tempFile = File('test_file.txt');
      await tempFile.writeAsString('Hello, World!');

      try {
        final result = await SyncUtils.encodeFileToBase64(tempFile.path);
        final expected = base64Encode('Hello, World!'.codeUnits);
        expect(result, equals(expected));
      } finally {
        // Clean up
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }
    });

    test('should detect URL format', () async {
      const httpUrl = 'http://example.com/file.txt';
      const httpsUrl = 'https://example.com/file.txt';

      // These will fail with network errors since we don't have real URLs
      // but they should at least recognize the URL format
      expect(
        () => SyncUtils.encodeFileToBase64(httpUrl),
        throwsException,
      );

      expect(
        () => SyncUtils.encodeFileToBase64(httpsUrl),
        throwsException,
      );
    });
  });
}
