import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/shared/cubits/connectivity_state.dart';

export 'connectivity_state.dart';

class ConnectivityCubit extends Cubit<ConnectivityState> {
  final NetworkInfo _networkInfo;
  StreamSubscription<InternetStatus>? _connectivitySubscription;

  ConnectivityCubit(this._networkInfo) : super(ConnectivityInitial()) {
    _initializeConnectivity();
  }

  void _initializeConnectivity() {
    // Check initial connectivity
    _checkConnectivity();

    // Listen for connectivity changes
    _connectivitySubscription = InternetConnection().onStatusChange.listen(
      (InternetStatus status) {
        switch (status) {
          case InternetStatus.connected:
            emit(ConnectivityOnline());
            break;
          case InternetStatus.disconnected:
            emit(ConnectivityOffline());
            break;
        }
      },
    );
  }

  Future<void> _checkConnectivity() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isClosed) {
        emit(isConnected ? ConnectivityOnline() : ConnectivityOffline());
      }
    } catch (e) {
      // If there's an error checking connectivity, assume offline for safety
      if (!isClosed) {
        emit(ConnectivityOffline());
      }
    }
  }

  /// Check connectivity manually if needed
  Future<void> checkConnectivity() => _checkConnectivity();

  /// Returns true if currently online
  bool get isOnline => state is ConnectivityOnline;

  /// Returns true if currently offline
  bool get isOffline => state is ConnectivityOffline;

  @override
  Future<void> close() {
    _connectivitySubscription?.cancel();
    return super.close();
  }
}
