import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/features/tutorial/presentation/widgets/page_indicator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';

class TutorialDialog extends StatefulWidget {
  final VoidCallback? onSkip;

  const TutorialDialog({
    super.key,
    this.onSkip,
  });

  @override
  State<TutorialDialog> createState() => _TutorialDialogState();

  static Future<void> show(BuildContext context, {VoidCallback? onSkip}) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      // barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return TutorialDialog(onSkip: onSkip);
      },
    );
  }
}

class _TutorialDialogState extends State<TutorialDialog> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> _tutorialImages = [
    AppAssets.tutorial1,
    AppAssets.tutorial2,
    AppAssets.tutorial3,
    AppAssets.tutorial4,
    AppAssets.tutorial5,
    AppAssets.tutorial6,
    AppAssets.tutorial7,
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _createTutorialPageWithResourceID(String imagePath, int pageIndex) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Image.asset(
                imagePath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppColors.lightGrey2,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.image_not_supported_outlined,
                          size: 48,
                          color: AppColors.blackTint1,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tutorial Image ${pageIndex + 1}',
                          style: const TextStyle(
                            fontFamily: AppFonts.montserrat,
                            fontSize: 16,
                            color: AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  Future<void> _skipTutorial() async {
    // Disable tutorial for future logins after skipping
    try {
      final dataManager = sl<DataManager>();
      await dataManager.saveTutorialEnabled(false);
    } catch (e) {
      // Handle error silently
    }

    if (mounted) {
      Navigator.of(context).pop();
      widget.onSkip?.call();
    }
  }

  Widget _buildCustomHeader() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(width: 48), // Balance the skip button
            const Text(
              'Tutorial',
              style: TextStyle(
                color: AppColors.black,
                fontFamily: AppFonts.montserrat,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            TextButton(
              onPressed: _skipTutorial,
              child: const Text(
                'Skip',
                style: TextStyle(
                  color: AppColors.black,
                  fontFamily: AppFonts.montserrat,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.white,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Custom header with title and skip button
              _buildCustomHeader(),

              // Main content area
              Expanded(
                child: Stack(
                  children: [
                    // Main ViewPager for tutorial images
                    PageView.builder(
                      controller: _pageController,
                      onPageChanged: _onPageChanged,
                      itemCount: _tutorialImages.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 28),
                          child: _createTutorialPageWithResourceID(
                            _tutorialImages[index],
                            index,
                          ),
                        );
                      },
                    ),

                    // Page indicators at bottom
                    Positioned(
                      bottom: 20,
                      left: 0,
                      right: 0,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: PageIndicator(
                          spacing: 2,
                          dotSize: 6,
                          inactiveColor: Colors.transparent,
                          inactiveBorderColor: AppColors.blackTint2,
                          currentPage: _currentPage,
                          totalPages: _tutorialImages.length,
                          onPageTap: (index) {
                            _pageController.animateToPage(
                              index,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
