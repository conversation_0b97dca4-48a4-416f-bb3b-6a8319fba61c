import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

class GlobalSyncIndicator extends StatelessWidget {
  final Widget child;

  const GlobalSyncIndicator({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: sl<SyncCubit>(),
      child: BlocBuilder<SyncCubit, SyncState>(
        builder: (context, state) {
          final isSyncing = state is SyncInProgress;

          return Stack(
            children: [
              child,
              if (isSyncing)
                Stack(
                  children: [
                    // Frosted glass effect
                    BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 4.0, sigmaY: 4.0),
                      child: Container(
                        color: Colors.black.withOpacity(0.2),
                      ),
                    ),
                    // Content
                    Center(
                      child: Material(
                        color: Colors.transparent,
                        child: Container(
                          padding: const EdgeInsets.all(24.0),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).cardColor.withOpacity(0.95),
                            borderRadius: BorderRadius.circular(16.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(height: 20),
                              Text(
                                "Syncing",
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratNavigationPrimaryMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          );
        },
      ),
    );
  }
}
