import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/pos_response_item_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/update_pos_usecase.dart';

part 'pos_state.dart';

class PosCubit extends Cubit<PosState> {
  final UpdatePosUseCase _updatePosUseCase;
  final DataManager _dataManager = sl<DataManager>();
  final FormRefreshCubit? _formRefreshCubit;

  PosCubit(this._updatePosUseCase, [this._formRefreshCubit])
      : super(PosInitial());

  Future<void> updatePosStatus({
    required TaskDetail task,
    required bool received,
  }) async {
    emit(PosLoading());

    try {
      // Get user credentials and device ID
      final userId = await _dataManager.getUserId();
      final token = await _dataManager.getAuthToken();
      final String deviceUid = await _dataManager.getOrCreateDeviceId();

      if (userId == null || token == null) {
        emit(const PosError('User credentials not found. Please login again.'));
        return;
      }

      // Create POS request entity
      final posRequestEntity = PosRequestEntity(
        taskId: task.taskId?.toInt(),
        storeName: task.storeName,
        clientName: task.client,
        cycle: task.cycle,
        rangeStart: task.rangeStart,
        rangeEnd: task.rangeEnd,
        scheduledDate: task.scheduledTimeStamp,
        received: received ? "true" : "false",
      );

      // Create wrapper request
      final updatePosRequest = UpdatePosRequestEntity(
        token: token,
        userId: int.tryParse(userId),
        pos: [posRequestEntity],
        deviceuid: deviceUid,
      );

      logger('Updating POS status: ${received ? "received" : "not received"}');
      logger('Request: ${updatePosRequest.toJson()}');

      // Call the use case
      final result = await _updatePosUseCase(updatePosRequest);

      if (result.isSuccess) {
        final response = result.data!;
        if (response.data?.pos?.isNotEmpty == true) {
          // Save POS response data to local database
          try {
            final realm = RealmDatabase.instance.realm;
            final posItems =
                PosResponseItemMapper.toModelList(response.data!.pos!);

            realm.write(() {
              // Remove existing POS response items for this task to avoid duplicates
              final existingItems = realm.query<PosResponseItemModel>(
                'taskId == ${task.taskId}',
              );
              realm.deleteMany(existingItems);

              // Add new POS response items
              realm.addAll(posItems);
            });

            logger(
                '✅ POS response data saved to database: ${posItems.length} items');
          } catch (e) {
            logger('❌ Error saving POS response data: $e');
            // Don't fail the operation if database save fails
          }

          emit(PosSuccess(
            message: received
                ? 'POS marked as received successfully'
                : 'POS marked as not received successfully',
            received: received,
          ));

          // Trigger refresh for TaskDetailsPage widgets
          _formRefreshCubit?.refresh();
        } else {
          emit(const PosError('Failed to update POS status'));
        }
      } else {
        emit(PosError(result.error ?? 'Failed to update POS status'));
      }
    } catch (e) {
      logger('Error updating POS status: $e');
      emit(PosError('An unexpected error occurred: $e'));
    }
  }

  void resetState() {
    emit(PosInitial());
  }
}
