import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

abstract class TimerState extends Equatable {
  const TimerState();

  @override
  List<Object?> get props => [];
}

class TimerInitial extends TimerState {}

class TimerRunning extends TimerState {
  final int taskId;
  final Duration elapsed;
  final Duration budget;
  final DateTime startTime;
  final List<ResumePauseItem> pauseItems;

  const TimerRunning({
    required this.taskId,
    required this.elapsed,
    required this.budget,
    required this.startTime,
    required this.pauseItems,
  });

  @override
  List<Object?> get props => [taskId, elapsed, budget, startTime, pauseItems];
}

class TimerPaused extends TimerState {
  final int taskId;
  final Duration elapsed;
  final Duration budget;
  final DateTime startTime;
  final List<ResumePauseItem> pauseItems;

  const TimerPaused({
    required this.taskId,
    required this.elapsed,
    required this.budget,
    required this.startTime,
    required this.pauseItems,
  });

  @override
  List<Object?> get props => [taskId, elapsed, budget, startTime, pauseItems];
}

class TimerStopped extends TimerState {
  final int taskId;
  final Duration totalElapsed;
  final Duration budget;
  final DateTime startTime;
  final DateTime stopTime;
  final List<ResumePauseItem> pauseItems;

  const TimerStopped({
    required this.taskId,
    required this.totalElapsed,
    required this.budget,
    required this.startTime,
    required this.stopTime,
    required this.pauseItems,
  });

  @override
  List<Object?> get props =>
      [taskId, totalElapsed, budget, startTime, stopTime, pauseItems];
}

class TimerError extends TimerState {
  final String message;

  const TimerError(this.message);

  @override
  List<Object?> get props => [message];
}
