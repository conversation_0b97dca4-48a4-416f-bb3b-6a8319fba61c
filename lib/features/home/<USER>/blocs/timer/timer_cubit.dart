import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/services/timer_service.dart';
import 'timer_state.dart';

class TimerCubit extends Cubit<TimerState> {
  final TimerService _timerService;
  Timer? _timer;
  int? _currentTaskId;

  TimerCubit(this._timerService) : super(TimerInitial()) {
    _initializeTimer();
  }

  Future<void> _initializeTimer() async {
    try {
      final activeTask = await _timerService.getActiveTimerTask();
      if (activeTask != null) {
        _currentTaskId = activeTask.taskId?.toInt();
        _startPeriodicTimer();
        await _updateTimerState();
      }
    } catch (e) {
      emit(TimerError('Failed to initialize timer: $e'));
    }
  }

  Future<void> startTimer(int taskId) async {
    try {
      // Check if another timer is already running
      final activeTask = await _timerService.getActiveTimerTask();
      if (activeTask != null && activeTask.taskId?.toInt() != taskId) {
        emit(TimerError(
            'Another timer is already running for task ${activeTask.taskId}'));
        return;
      }

      await _timerService.startTimer(taskId);
      _currentTaskId = taskId;

      // Only start periodic timer if not already running
      if (_timer == null || !_timer!.isActive) {
        _startPeriodicTimer();
      }

      await _updateTimerState();
    } catch (e) {
      emit(TimerError('Failed to start timer: $e'));
    }
  }

  Future<void> pauseTimer() async {
    try {
      if (_currentTaskId == null) return;

      await _timerService.pauseTimer(_currentTaskId!);

      // Stop the periodic timer when paused to prevent race conditions
      _timer?.cancel();
      _timer = null;

      await _updateTimerState();
    } catch (e) {
      emit(TimerError('Failed to pause timer: $e'));
    }
  }

  Future<void> resumeTimer() async {
    try {
      if (_currentTaskId == null) return;

      // Check if another timer is already running
      final activeTask = await _timerService.getActiveTimerTask();
      if (activeTask != null && activeTask.taskId?.toInt() != _currentTaskId) {
        emit(TimerError(
            'Another timer is already running for task ${activeTask.taskId}'));
        return;
      }

      await _timerService.resumeTimer(_currentTaskId!);

      // Restart the periodic timer when resuming
      _startPeriodicTimer();

      await _updateTimerState();
    } catch (e) {
      emit(TimerError('Failed to resume timer: $e'));
    }
  }

  Future<void> stopTimer() async {
    try {
      if (_currentTaskId == null) return;

      await _timerService.stopTimer(_currentTaskId!);
      _timer?.cancel();
      _timer = null;
      _currentTaskId = null;
      await _updateTimerState();
    } catch (e) {
      emit(TimerError('Failed to stop timer: $e'));
    }
  }

  Future<void> _updateTimerState() async {
    try {
      if (_currentTaskId == null) {
        emit(TimerInitial());
        return;
      }

      final task = await _timerService.getTaskById(_currentTaskId!);
      if (task == null) {
        emit(TimerInitial());
        return;
      }

      final elapsed = _timerService.calculateElapsedTime(task);
      final budget = Duration(minutes: task.budget?.toInt() ?? 0);
      final pauseItems = task.resumePauseItems ?? [];

      if (task.taskCommencementTimeStamp != null &&
          task.taskStoppedTimeStamp == null) {
        // Timer is active
        if (_timerService.isTimerPaused(task)) {
          emit(TimerPaused(
            taskId: _currentTaskId!,
            elapsed: elapsed,
            budget: budget,
            startTime: task.taskCommencementTimeStamp!,
            pauseItems: pauseItems,
          ));
        } else {
          emit(TimerRunning(
            taskId: _currentTaskId!,
            elapsed: elapsed,
            budget: budget,
            startTime: task.taskCommencementTimeStamp!,
            pauseItems: pauseItems,
          ));
        }
      } else if (task.taskCommencementTimeStamp != null &&
          task.taskStoppedTimeStamp != null) {
        // Timer is stopped
        emit(TimerStopped(
          taskId: _currentTaskId!,
          totalElapsed: elapsed,
          budget: budget,
          startTime: task.taskCommencementTimeStamp!,
          stopTime: task.taskStoppedTimeStamp!,
          pauseItems: pauseItems,
        ));
      } else {
        emit(TimerInitial());
      }
    } catch (e) {
      emit(TimerError('Failed to update timer state: $e'));
    }
  }

  void _startPeriodicTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimerState();
    });
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
