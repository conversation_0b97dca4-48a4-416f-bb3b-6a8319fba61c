import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class StoreHistoryPage extends StatelessWidget {
  final int storeId;
  final int taskId;

  const StoreHistoryPage({
    super.key,
    required this.storeId,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<StoreHistoryCubit>(),
      child: _StoreHistoryPageContent(
        storeId: storeId,
        taskId: taskId,
      ),
    );
  }
}

class _StoreHistoryPageContent extends StatefulWidget {
  final int storeId;
  final int taskId;

  const _StoreHistoryPageContent({
    required this.storeId,
    required this.taskId,
  });

  @override
  State<_StoreHistoryPageContent> createState() =>
      _StoreHistoryPageContentState();
}

class _StoreHistoryPageContentState extends State<_StoreHistoryPageContent> {
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  late String actualUserToken;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      // Fetch previous tasks
      if (mounted) {
        context.read<StoreHistoryCubit>().fetchPreviousTasks(
              PreviousTasksRequestEntity(
                userId: int.tryParse(actualUserId) ?? 0,
                token: actualUserToken,
                deviceUid: actualDeviceUid,
                appversion: actualAppVersion,
                taskId: widget.taskId,
                specificTaskId: widget.storeId,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize store history: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<StoreHistoryCubit, StoreHistoryState>(
      listener: (context, state) {
        if (state is StoreHistoryError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: const CustomAppBar(
            title: 'Store history',
          ),
          body: _buildBody(state),
        );
      },
    );
  }

  Widget _buildBody(StoreHistoryState state) {
    if (state is StoreHistoryLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    } else if (state is StoreHistoryLoaded) {
      return state.previousTasks.isEmpty
          ? _buildEmptyState()
          : _buildTaskList(state.previousTasks);
    } else if (state is StoreHistoryError) {
      return _buildEmptyState();
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(0),
      child:
          const EmptyState(message: 'No previous task information available'),
    );
  }

  Widget _buildTaskList(List<PreviousTaskEntity> previousTasks) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: previousTasks.length,
      itemBuilder: (context, index) {
        final task = previousTasks[index];
        return _buildTaskCard(task, index);
      },
    );
  }

  Widget _buildTaskCard(PreviousTaskEntity task, int index) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => _onTaskTap(task),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Cycle Name
                      Text(
                        task.cycleName ?? 'Unknown Cycle',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleSmall
                            .copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const Gap(8),

                      // Store Name
                      _buildInfoRow('Store:', "Example Store" ?? 'Unknown'),
                      const Gap(4),

                      // Date Scheduled
                      _buildInfoRow(
                          'Date Scheduled:',
                          task.dateSchedule != null
                              ? dateFormat.format(task.dateSchedule!)
                              : 'N/A'),
                      const Gap(4),

                      // Task Comment
                      _buildInfoRow('Comment:', 'No comment'),
                    ],
                  ),
                ),
                const Gap(8),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.blackTint1,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 110,
          child: Text(
            label,
            style:
                Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.black,
                    ),
          ),
        ),
        const Gap(8),
        Expanded(
          child: Text(
            value,
            style:
                Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                      color: AppColors.black,
                    ),
          ),
        ),
      ],
    );
  }

  void _onTaskTap(PreviousTaskEntity task) {
    // Handle task tap - similar to Android implementation
    // This would navigate to a detailed view of the previous task
    // For now, we'll just show a snackbar
    if (mounted) {
      SnackBarService.info(
        context: context,
        message: 'Tapped on ${task.cycleName ?? 'Unknown Task'}',
      );
    }
  }
}
