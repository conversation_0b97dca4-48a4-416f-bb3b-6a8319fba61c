import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/open_tasks/open_tasks_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/open_tasks/open_tasks_state.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import '../../../../core/storage/data_manager.dart';
// import '../widgets/open_tasks_app_bar.dart';
import '../widgets/reorderable_store_list.dart';
import '../widgets/empty_state.dart';
import '../widgets/view_toggle_buttons.dart';

@RoutePage()
class OpenTasksPage extends StatefulWidget {
  const OpenTasksPage({super.key});

  @override
  State<OpenTasksPage> createState() => _OpenTasksPageState();
}

class _OpenTasksPageState extends State<OpenTasksPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool isWeekView = true;
  late DateTime selectedDate;
  DateTime? selectedCalendarDate;
  List<DateTime> weekDays = [];
  final DateTime _currentMonth = DateTime.now();
  List<DateTime> monthDays = [];

  // Track which button is currently selected
  String _selectedButton =
      'all'; // Options: 'all', 'this_week', 'next_week', 'overdue'

  // Task data
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  late String actualUserToken;

  List<TaskDetail> openTasks = [];
  bool _isCheckboxMode = false;
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Store all tasks from the API response
  List<TaskDetail> allApiTasks = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Set initial selected button based on tab
    _selectedButton = _tabController.index == 0
        ? 'all'
        : _tabController.index == 1
            ? 'this_week'
            : _tabController.index == 2
                ? 'next_week'
                : 'overdue';

    // Initialize selectedDate
    selectedDate = DateTime.now();

    // Initialize the week and month days
    _generateWeekDays();
    _generateMonthDays();

    // Initialize data
    _initializeData();
  }

  void _generateWeekDays() {
    // Get the start of the week (Monday)
    final DateTime now = selectedDate;
    final int day = now.weekday;
    final DateTime firstDayOfWeek = now.subtract(Duration(days: day - 1));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });

    // Check if today is in this week, if so select it
    final today = DateTime.now();
    final bool isTodayInWeek = weekDays.any((day) =>
        day.day == today.day &&
        day.month == today.month &&
        day.year == today.year);

    if (isTodayInWeek) {
      // Select today
      selectedDate = today;
    }
  }

  void _generateMonthDays() {
    // Get the first day of the month
    final DateTime firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);

    // Get the weekday of the first day (0 = Monday, 6 = Sunday)
    final int firstWeekday = firstDayOfMonth.weekday - 1;

    // Calculate days from previous month to show
    final DateTime firstDayToShow =
        firstDayOfMonth.subtract(Duration(days: firstWeekday));

    // Calculate total days to show (6 weeks = 42 days)
    monthDays = List.generate(42, (index) {
      return firstDayToShow.add(Duration(days: index));
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        if (_tabController.index == 0) {
          _selectedButton = 'all';
          isWeekView = true;
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
        } else if (_tabController.index == 1) {
          _selectedButton = 'this_week';
          isWeekView = true;
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 2) {
          _selectedButton = 'next_week';
          isWeekView = true;
          final DateTime nextWeekDate =
              DateTime.now().add(const Duration(days: 7));
          selectedDate = nextWeekDate;
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 3) {
          _selectedButton = 'overdue';
          isWeekView = false;
        }

        // Update tasks for the selected view
        _updateOpenTasksForView();
      });
    }
  }

  void _updateOpenTasksForView() {
    List<TaskDetail> tasksToDisplay = [];

    if (_selectedButton == 'all') {
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.isOpen == true &&
              task.taskStatus == "Tentative" &&
              task.taskId != 0)
          .toList();
    } else if (_selectedButton == 'this_week') {
      tasksToDisplay = allApiTasks.where((task) {
        if (task.isOpen != true ||
            task.taskStatus != "Tentative" ||
            task.taskId == 0) {
          return false;
        }
        // For open tasks, we'll show them if they're available this week
        // Since open tasks don't have scheduled dates, we'll show all open tasks for this week
        return true;
      }).toList();
    } else if (_selectedButton == 'next_week') {
      tasksToDisplay = allApiTasks.where((task) {
        if (task.isOpen != true ||
            task.taskStatus != "Tentative" ||
            task.taskId == 0) {
          return false;
        }
        // For open tasks, we'll show them if they're available next week
        // Since open tasks don't have scheduled dates, we'll show all open tasks for next week
        return true;
      }).toList();
    } else if (_selectedButton == 'overdue') {
      // For open tasks, "overdue" would mean tasks that have been open for a long time
      // We'll show all open tasks as they could be considered overdue
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.isOpen == true &&
              task.taskStatus == "Tentative" &&
              task.taskId != 0)
          .toList();
    }

    setState(() {
      openTasks = tasksToDisplay
        ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
    });
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      if (mounted) {
        context.read<OpenTasksCubit>().getOpenTasks(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  void _filterOpenTasks(List<TaskDetail> allTasks) {
    allApiTasks = allTasks;
    _updateOpenTasksForView();
  }

  void _handleAcceptTask() async {
    if (selectedItems.isEmpty) {
      SnackBarService.warning(
        context: context,
        message: 'Please select at least one task to accept.',
      );
      return;
    }

    try {
      for (var task in selectedItems) {
        await context.read<OpenTasksCubit>().acceptTask(
              task.taskId.toString(),
              actualUserId,
              actualUserToken,
              actualDeviceUid,
              actualAppVersion,
            );
      }

      // Clear selection after accepting tasks
      if (mounted) {
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });

        // Refresh data to show updated list
        await _refreshData();
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to accept tasks: ${e.toString()}',
        );
      }
    }
  }

  void _handleSelectionChanged(
      List<TaskDetail> itemsInList, List<TaskDetail> selectedItemsInList) {
    setState(() {
      final itemsInListIds = itemsInList.map((t) => t.taskId).toSet();
      selectedItems.removeWhere((task) => itemsInListIds.contains(task.taskId));
      selectedItems.addAll(selectedItemsInList);
      _areAllItemsSelected =
          selectedItems.length == openTasks.length && openTasks.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OpenTasksCubit, OpenTasksState>(
      listener: (context, state) {
        if (state is OpenTasksSuccess) {
          setState(() {
            var response = state.response;
            var allTasks = response.addTasks ?? [];
            _filterOpenTasks(allTasks);
          });
        } else if (state is OpenTasksError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        } else if (state is AcceptTaskSuccess) {
          SnackBarService.success(
            context: context,
            message: state.message,
          );
        } else if (state is AcceptTaskError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: CustomAppBar(
            title: 'Open Tasks',
            bottom: ViewToggleButtons(
              selectedButton: _selectedButton,
              tabController: _tabController,
              onButtonPressed: (buttonKey) {
                setState(() {
                  _selectedButton = buttonKey;
                  if (buttonKey == 'all') {
                    _tabController.animateTo(0);
                  } else if (buttonKey == 'this_week') {
                    _tabController.animateTo(1);
                  } else if (buttonKey == 'next_week') {
                    _tabController.animateTo(2);
                  } else if (buttonKey == 'overdue') {
                    _tabController.animateTo(3);
                  }
                });
              },
            ),
            actions: [
              GestureDetector(
                onTap: _handleAcceptTask,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'Accept',
                    style: Theme.of(context)
                        .textTheme
                        .montserratTitleExtraSmall
                        .copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCheckboxMode = !_isCheckboxMode;
                    if (!_isCheckboxMode) {
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    }
                  });
                },
                child: Image.asset(
                  AppAssets.appbarCalendarEdit,
                  color:
                      _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
                  scale: 4,
                ),
              ),
              const Gap(16),
            ],
          ),
          body: BlocListener<SyncCubit, SyncState>(
            listener: (context, state) {
              if (state is SyncSuccess) {
                _refreshData();
              }
            },
            child: RefreshIndicator(
              onRefresh: _initializeData,
              child: _buildScrollableContent(context),
            ),
          ),
        );
      },
    );
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    final currentState = context.read<OpenTasksCubit>().state;

    if (currentState is OpenTasksLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          if (_selectedButton == 'all')
            // For "All" tab, show all open tasks
            _buildAllOpenTasksList()
          else if (_selectedButton == 'this_week' ||
              _selectedButton == 'next_week')
            // For week tabs, show open tasks for the selected week
            _buildWeekOpenTasksList()
          else if (_selectedButton == 'overdue')
            // For overdue tab, show overdue open tasks
            _buildOverdueOpenTasksList()
          else
            // Fallback for any other state
            Container(
              color: AppColors.lightGrey2,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Select a view option',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAllOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No open tasks available');
    }

    return ReorderableStoreList(
      tasks: openTasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: false,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }

  Widget _buildWeekOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No open tasks for this week');
    }

    return ReorderableStoreList(
      tasks: openTasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: false,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }

  Widget _buildOverdueOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No overdue open tasks');
    }

    return ReorderableStoreList(
      tasks: openTasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: false,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }
}
