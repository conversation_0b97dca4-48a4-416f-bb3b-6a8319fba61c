import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/availability_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/history_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/useful_links_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/update_profile_request.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_response.dart';
import 'package:storetrack_app/features/home/<USER>/models/notification_req.dart';
import '../entities/checkin_request_entity.dart';
import '../entities/checkin_response_entity.dart';
import '../entities/vacancy_entity.dart';
import '../../data/models/vacancy_model.dart';
import '../entities/pos_request_entity.dart';
import '../entities/pos_response_entity.dart';
import '../entities/upload_photo_request_entity.dart';
import '../entities/upload_photo_response_entity.dart';
import '../entities/upload_sign_request_entity.dart';
import '../entities/upload_signature_response_entity.dart';
import '../entities/sync_pic_request_entity.dart';
import '../entities/sync_pic_response_entity.dart';
import '../entities/sync_sign_request_entity.dart';
import '../entities/sync_signature_response_entity.dart';

import '../../../../shared/models/result.dart';
import '../../data/datasources/home_local_datasource.dart';
import '../../data/datasources/home_remote_datasource.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/calendar_response_entity.dart';
import '../usecases/get_calendar_usecase.dart';
import '../entities/submit_report_request_entity.dart';
import '../entities/submit_report_response_entity.dart';
import '../entities/previous_tasks_response_entity.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final HomeLocalDataSource localDataSource;

  HomeRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.localDataSource,
  });

  @override
  Future<Result<TasksResponseEntity>> getTasks(
    TasksRequestEntity request, {
    bool isSync = false,
  }) async {
    // If syncing, always fetch from remote
    if (isSync) {
      return _fetchAndCache(
        () => remoteDataSource.getTasks(request),
        (data) => localDataSource.saveTasks(data),
      );
    }

    // "Cache-first" strategy
    final localData = await localDataSource.getTasks();
    if (localData != null) {
      return Result.success(localData);
    }

    // If cache is empty, fetch from remote
    if (await networkInfo.isConnected) {
      return _fetchAndCache(
        () => remoteDataSource.getTasks(request),
        (data) => localDataSource.saveTasks(data),
      );
    } else {
      return Result.failure(
          'No internet connection and no cached data available.');
    }
  }

  Future<Result<T>> _fetchAndCache<T>(
    Future<Result<T>> Function() remoteFetcher,
    Future<void> Function(T data) localSaver,
  ) async {
    final result = await remoteFetcher();
    if (result.isSuccess && result.data != null) {
      await localSaver(result.data as T);
    }
    return result;
  }

  @override
  Future<Result<TaskDetail>> getTaskDetail(int taskId) async {
    // Always fetch from local database since tasks are cached locally
    final taskDetail = await localDataSource.getTaskDetail(taskId);
    if (taskDetail != null) {
      return Result.success(taskDetail);
    } else {
      return Result.failure(
          'Task with ID $taskId not found in local database.');
    }
  }

  @override
  Future<Result<CalendarResponseEntity>> getCalendarData(
    GetCalendarParams request, {
    bool isSync = false,
  }) async {
    if (isSync) {
      return _fetchAndCache(
        () => remoteDataSource.getCalendarData(request),
        (data) => localDataSource.saveCalendarInfo(data),
      );
    }

    // "Cache-first" strategy
    final localData = await localDataSource.getCalendarInfo();
    if (localData != null) {
      return Result.success(localData);
    }

    // If cache is empty, fetch from remote
    if (await networkInfo.isConnected) {
      return _fetchAndCache(
        () => remoteDataSource.getCalendarData(request),
        (data) => localDataSource.saveCalendarInfo(data),
      );
    } else {
      return Result.failure(
          'No internet connection and no cached calendar data available.');
    }
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request) async {
    return remoteDataSource.submitReport(request);
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReportBatch(
      SubmitReportRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.submitReportBatch(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasks(
      PreviousTasksRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote
      return await remoteDataSource.getPreviousTasks(request);
    } else {
      // Offline: return failure
      return Result.failure(
          'No internet connection. Previous tasks require an active connection.');
    }
  }

  @override
  Future<Result<PreviousTasksResponseEntity>> getPreviousTasksOptimize(
      PreviousTasksRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote
      return await remoteDataSource.getPreviousTasksOptimize(request);
    } else {
      // Offline: return failure
      return Result.failure(
          'No internet connection. Optimized previous tasks require an active connection.');
    }
  }

  @override
  Future<Result<UsefulLinksResponse>> getUsefulLinks({
    required String token,
    required String userId,
  }) async {
    return await remoteDataSource.getUsefulLinks(
      token: token,
      userId: userId,
    );
  }

  @override
  Future<Result<InductionResponse>> getInduction({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getInduction(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<HistoryResponse>> getHistory({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getHistory(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<SkillsResponse>> getSkills({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getSkills(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<AvailabilityResponse>> getAvailability({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getAvailability(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveInduction({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveInduction(
        token: token,
        userId: userId,
        inductions: inductions,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveSkills({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> skills,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveSkills(
        token: token,
        userId: userId,
        skills: skills,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveAvailability({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> days,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveAvailability(
        token: token,
        userId: userId,
        days: days,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<LeaveResponse>> getLeave({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getLeave(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> deleteLeave({
    required String token,
    required String userId,
    required List<int> leaveIds,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.deleteLeave(
        token: token,
        userId: userId,
        leaveIds: leaveIds,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> addLeave({
    required String token,
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.addLeave(
        token: token,
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreContactResponse>> getStoreContacts({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreContacts(
        token: token,
        userId: userId,
        storeId: storeId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveStoreContact({
    required StoreContactRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveStoreContact(
        request: request,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreContactTypesResponse>> getStoreContactTypes({
    required String token,
    required String userId,
    required String storeId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreContactTypes(
        token: token,
        userId: userId,
        storeId: storeId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<StoreCommentsResponse>> getStoreComments({
    required String taskId,
    required String userId,
    required String token,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getStoreComments(
        taskId: taskId,
        userId: userId,
        token: token,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> saveStoreComment({
    required StoreCommentRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.saveStoreComment(
        request: request,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<String>> getAutoScheduleLink({
    required String token,
    required String userId,
    int dayOffset = 0,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getAutoScheduleLink(
        token: token,
        userId: userId,
        dayOffset: dayOffset,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<CheckinResponseEntity>> sendCheckin(
      CheckinRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.sendCheckin(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getProfile(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.updateProfile(
        request: request,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<NotificationResponse>> getAlerts(
      NotificationReqParams request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getAlerts(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<List<VacancyEntity>>> getVacancies({
    required String token,
    required String userId,
  }) async {
    try {
      // Mock delay to simulate network call
      await Future.delayed(const Duration(seconds: 1));

      // Mock vacancy data
      final mockVacancies = [
        VacancyModel(
          id: 1,
          jobTitle: "Store Assistant",
          jobLocation: "Sydney, NSW",
          jobDescription:
              "We are looking for a reliable Store Assistant to join our team. You will be responsible for customer service, stock management, and maintaining store presentation.",
          companyName: "Coles Supermarkets",
          salaryRange: "\$25 - \$30 per hour",
          employmentType: "Part-time",
          postedDate: DateTime.now().subtract(const Duration(days: 2)),
          canApply: true,
          canRefer: true,
        ),
        VacancyModel(
          id: 2,
          jobTitle: "Merchandiser",
          jobLocation: "Melbourne, VIC",
          jobDescription:
              "Seeking an experienced Merchandiser to ensure optimal product placement and visual presentation. Experience in retail merchandising preferred.",
          companyName: "Woolworths Group",
          salaryRange: "\$28 - \$35 per hour",
          employmentType: "Full-time",
          postedDate: DateTime.now().subtract(const Duration(days: 5)),
          canApply: true,
          canRefer: true,
        ),
      ];

      final entities = mockVacancies.map((model) => model.toEntity()).toList();
      return Result.success(entities);
    } catch (e) {
      return Result.failure(e.toString());
    }
  }

  @override
  Future<Result<bool>> applyToVacancy({
    required String token,
    required String userId,
    required int vacancyId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.applyToVacancy(
        token: token,
        userId: userId,
        vacancyId: vacancyId,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> referVacancy({
    required String token,
    required String userId,
    required int vacancyId,
    required String refereeEmail,
  }) async {
    try {
      // Mock delay to simulate network call
      await Future.delayed(const Duration(milliseconds: 500));

      // Mock successful referral
      return Result.success(true);
    } catch (e) {
      return Result.failure(e.toString());
    }
  }

  @override
  Future<Result<PosResponseEntity>> updatePos(
      UpdatePosRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.updatePos(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> referVacancyWithDetails({
    required String token,
    required String userId,
    required int vacancyId,
    required String name,
    required String email,
    required String phone,
    required String stateName,
    required String suburb,
    required String comment,
    required String preference,
  }) async {
    print('DEBUG: HomeRepositoryImpl.referVacancyWithDetails called');
    print('DEBUG: Network connected: ${await networkInfo.isConnected}');

    if (await networkInfo.isConnected) {
      print('DEBUG: Calling remote data source');
      final result = await remoteDataSource.referVacancyWithDetails(
        token: token,
        userId: userId,
        vacancyId: vacancyId,
        name: name,
        email: email,
        phone: phone,
        stateName: stateName,
        suburb: suburb,
        comment: comment,
        preference: preference,
      );
      print(
          'DEBUG: Remote data source result: ${result.isSuccess} - ${result.error}');
      return result;
    } else {
      print('DEBUG: No network connection');
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<UploadPhotoResponseEntity>> uploadPhoto(
      UploadPhotoRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.uploadPhoto(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<UploadPhotoResponseEntity>> uploadPhotoWithHandling({
    required UploadPhotoRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.uploadPhotoWithHandling(
        request: request,
        taskId: taskId,
        originalLocalPath: originalLocalPath,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<SyncPicResponseEntity>> syncPhotoInfo(
      SyncPicInfoRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.syncPhotoInfo(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<UploadSignatureResponseEntity>> uploadSignature(
      UploadSignRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.uploadSignature(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<UploadSignatureResponseEntity>> uploadSignatureWithHandling({
    required UploadSignRequestEntity request,
    required int taskId,
    required String originalLocalPath,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.uploadSignatureWithHandling(
        request: request,
        taskId: taskId,
        originalLocalPath: originalLocalPath,
      );
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<SyncSignatureResponseEntity>> syncSignatureInfo(
      SyncSignInfoRequestEntity request) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.syncSignatureInfo(request);
    } else {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }
  }

  @override
  Future<Result<bool>> uploadAndSyncPhotos({
    required List<TaskDetail> tasks,
  }) async {
    if (!await networkInfo.isConnected) {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }

    try {
      logger('🚀 Starting comprehensive photo upload and sync workflow');

      // Step 1: Batch Photo Upload with Response Handling
      final uploadSuccess = await _batchUploadPhotos(tasks);
      if (!uploadSuccess) {
        logger('⚠️ Some photo uploads failed, but continuing with sync');
      }

      // Step 2: Photo Metadata Sync
      final syncResult = await _syncPhotoMetadata(tasks);
      if (syncResult.isFailure) {
        logger('❌ Photo metadata sync failed: ${syncResult.error}');
        return Result.failure('Photo sync failed: ${syncResult.error}');
      }

      // Step 3: Post-Sync Cleanup
      final cleanupSuccess = await _cleanupSoftDeletedPhotos();
      if (!cleanupSuccess) {
        logger('⚠️ Photo cleanup had some issues, but sync completed');
      }

      logger('✅ Photo upload and sync workflow completed successfully');
      return Result.success(true);
    } catch (e) {
      logger('❌ Error in photo upload and sync workflow: $e');
      return Result.failure(
          'Photo upload and sync failed due to an unexpected error. Please try again.');
    }
  }

  /// Step 1: Batch upload all photos with automatic response handling
  Future<bool> _batchUploadPhotos(List<TaskDetail> tasks) async {
    try {
      logger('📤 Starting batch photo upload');

      // Get all photos that need to be uploaded
      final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
      logger('Found ${photosToUpload.length} photos to upload');

      if (photosToUpload.isEmpty) {
        logger('No photos to upload');
        return true;
      }

      int successCount = 0;
      int failureCount = 0;

      // Upload each photo individually with response handling
      for (final photoData in photosToUpload) {
        try {
          final photo = photoData['photo'] as Photo;
          final taskId = photoData['taskId'] as int;
          final folderId = photoData['folderId'] as int;
          final originalLocalPath = photo.localPath ?? '';

          // if (originalLocalPath.isEmpty) {
          //   logger('⚠️ Skipping photo without local path: ${photo.photoId}');
          //   continue;
          // }

          // Create upload request
          final uploadRequest = await SyncUtils.createUploadPhotoRequest(
            photo: photo,
            taskId: taskId,
            folderId: folderId,
          );

          // Upload with automatic response handling
          final uploadResult = await uploadPhotoWithHandling(
            request: uploadRequest,
            taskId: taskId,
            originalLocalPath: originalLocalPath,
          );

          if (uploadResult.isSuccess) {
            successCount++;
            logger('✅ Photo uploaded successfully: ${photo.photoId}');
          } else {
            failureCount++;
            logger(
                '❌ Photo upload failed: ${photo.photoId} - ${uploadResult.error}');
          }
        } catch (e) {
          failureCount++;
          logger('❌ Error uploading individual photo: $e');
        }
      }

      logger(
          '📤 Batch upload completed: $successCount success, $failureCount failures');

      // Return true if at least some uploads succeeded, or if there were no photos
      return failureCount == 0 || successCount > 0;
    } catch (e) {
      logger('❌ Error in batch photo upload: $e');
      return false;
    }
  }

  /// Step 2: Sync photo metadata after all uploads complete
  Future<Result<SyncPicResponseEntity>> _syncPhotoMetadata(
      List<TaskDetail> tasks) async {
    try {
      logger('🔄 Starting photo metadata sync');

      // Create sync request
      final syncRequest =
          await SyncUtils.createSyncPicInfoRequest(tasks: tasks);

      // Perform sync
      final syncResult = await syncPhotoInfo(syncRequest);

      if (syncResult.isSuccess) {
        logger('✅ Photo metadata sync completed successfully');
      } else {
        logger('❌ Photo metadata sync failed: ${syncResult.error}');
      }

      return syncResult;
    } catch (e) {
      logger('❌ Error in photo metadata sync: $e');
      return Result.failure('Photo metadata sync failed: $e');
    }
  }

  /// Step 3: Post-sync cleanup of soft-deleted photos
  Future<bool> _cleanupSoftDeletedPhotos() async {
    try {
      // Use the reusable PhotoUtils method for cleanup
      final cleanupResult = await PhotoUtils.cleanupSoftDeletedPhotos();

      final deletedCount = cleanupResult['deletedCount'] as int;
      final errorCount = cleanupResult['errorCount'] as int;
      final success = cleanupResult['success'] as bool;

      if (success) {
        logger(
            '✅ Photo cleanup completed: $deletedCount deleted, $errorCount errors');
      } else {
        logger(
            '⚠️ Photo cleanup had issues: $deletedCount deleted, $errorCount errors');
      }

      return success;
    } catch (e) {
      logger('❌ Error in photo cleanup: $e');
      return false;
    }
  }

  @override
  Future<Result<bool>> uploadAndSyncSignatures({
    required List<TaskDetail> tasks,
  }) async {
    if (!await networkInfo.isConnected) {
      return Result.failure(
          'No internet connection. Please try again when online.');
    }

    try {
      logger('🚀 Starting comprehensive signature upload and sync workflow');

      // Step 1: Batch Signature Upload with Response Handling
      final uploadSuccess = await _batchUploadSignatures(tasks);
      if (!uploadSuccess) {
        logger('⚠️ Some signature uploads failed, but continuing with sync');
      }

      // Step 2: Signature Metadata Sync
      final syncResult = await _syncSignatureMetadata(tasks);
      if (syncResult.isFailure) {
        logger('❌ Signature metadata sync failed: ${syncResult.error}');
        return Result.failure('Signature sync failed: ${syncResult.error}');
      }

      // Step 3: Post-Sync Cleanup
      final cleanupSuccess = await _cleanupSoftDeletedSignatures();
      if (!cleanupSuccess) {
        logger('⚠️ Signature cleanup had some issues, but sync completed');
      }

      logger('✅ Signature upload and sync workflow completed successfully');
      return Result.success(true);
    } catch (e) {
      logger('❌ Error in signature upload and sync workflow: $e');
      return Result.failure(
          'Signature upload and sync failed due to an unexpected error. Please try again.');
    }
  }

  /// Step 1: Batch upload all signatures with automatic response handling
  Future<bool> _batchUploadSignatures(List<TaskDetail> tasks) async {
    try {
      logger('📤 Starting batch signature upload');

      // Get all signatures that need to be uploaded
      final signaturesToUpload = SyncUtils.getSignaturesToUpload(tasks);
      logger('Found ${signaturesToUpload.length} signatures to upload');

      if (signaturesToUpload.isEmpty) {
        logger('No signatures to upload');
        return true;
      }

      int successCount = 0;
      int failureCount = 0;

      // Upload each signature individually with response handling
      for (final signatureData in signaturesToUpload) {
        try {
          final signature = signatureData['signature'] as Signature;
          final taskId = signatureData['taskId'] as int;
          final folderId = signatureData['folderId'] as int;
          final originalLocalPath = signature.localPath ?? '';

          // if (originalLocalPath.isEmpty) {
          //   logger(
          //       '⚠️ Skipping signature without local path: ${signature.signatureId}');
          //   continue;
          // }

          // Create upload request
          final uploadRequest = await SyncUtils.createUploadSignatureRequest(
            signature: signature,
            taskId: taskId,
            folderId: folderId,
          );

          // Upload with automatic response handling
          final uploadResult = await uploadSignatureWithHandling(
            request: uploadRequest,
            taskId: taskId,
            originalLocalPath: originalLocalPath,
          );

          if (uploadResult.isSuccess) {
            successCount++;
            logger(
                '✅ Signature uploaded successfully: ${signature.signatureId}');
          } else {
            failureCount++;
            logger(
                '❌ Signature upload failed: ${signature.signatureId} - ${uploadResult.error}');
          }
        } catch (e) {
          failureCount++;
          logger('❌ Error uploading individual signature: $e');
        }
      }

      logger(
          '📤 Batch upload completed: $successCount success, $failureCount failures');

      // Return true if at least some uploads succeeded, or if there were no signatures
      return failureCount == 0 || successCount > 0;
    } catch (e) {
      logger('❌ Error in batch signature upload: $e');
      return false;
    }
  }

  /// Step 2: Sync signature metadata after all uploads complete
  Future<Result<SyncSignatureResponseEntity>> _syncSignatureMetadata(
      List<TaskDetail> tasks) async {
    try {
      logger('🔄 Starting signature metadata sync');

      // Create sync request
      final syncRequest =
          await SyncUtils.createSyncSignatureInfoRequest(tasks: tasks);

      // Perform sync
      final syncResult = await syncSignatureInfo(syncRequest);

      if (syncResult.isSuccess) {
        logger('✅ Signature metadata sync completed successfully');
      } else {
        logger('❌ Signature metadata sync failed: ${syncResult.error}');
      }

      return syncResult;
    } catch (e) {
      logger('❌ Error in signature metadata sync: $e');
      return Result.failure('Signature metadata sync failed: $e');
    }
  }

  /// Step 3: Post-sync cleanup of soft-deleted signatures
  Future<bool> _cleanupSoftDeletedSignatures() async {
    try {
      // Use the reusable SignatureUtils method for cleanup
      final cleanupResult = await SignatureUtils.cleanupSoftDeletedSignatures();

      final deletedCount = cleanupResult['deletedCount'] as int;
      final errorCount = cleanupResult['errorCount'] as int;
      final success = cleanupResult['success'] as bool;

      if (success) {
        logger(
            '✅ Signature cleanup completed: $deletedCount deleted, $errorCount errors');
      } else {
        logger(
            '⚠️ Signature cleanup had issues: $deletedCount deleted, $errorCount errors');
      }

      return success;
    } catch (e) {
      logger('❌ Error in signature cleanup: $e');
      return false;
    }
  }
}
