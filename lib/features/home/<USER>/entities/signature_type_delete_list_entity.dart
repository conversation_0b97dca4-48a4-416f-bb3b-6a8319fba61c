import 'dart:convert';

/// Entity class for signature type deletion operations
/// Used for delete_signaturetypes processing
class SignatureTypeDeleteListModel {
  String? taskId;
  List<String> signaturetypeIDsToBeDeleted;

  SignatureTypeDeleteListModel({
    this.taskId,
    this.signaturetypeIDsToBeDeleted = const [],
  });

  factory SignatureTypeDeleteListModel.fromRawJson(String str) =>
      SignatureTypeDeleteListModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SignatureTypeDeleteListModel.fromJson(Map<String, dynamic> json) =>
      SignatureTypeDeleteListModel(
        taskId: json["task_id"]?.toString(),
        signaturetypeIDsToBeDeleted:
            json["signaturetype_ids_to_be_deleted"] == null
                ? []
                : List<String>.from(json["signaturetype_ids_to_be_deleted"]!
                    .map((x) => x.toString())),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "signaturetype_ids_to_be_deleted": signaturetypeIDsToBeDeleted,
      };

  // Getter for compatibility with process_core.md naming
  String getTaskID() => taskId ?? "";
}
