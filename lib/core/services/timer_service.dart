import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/core/utils/logger.dart';

class TimerService {
  final RealmDatabase _database;
  final TaskDetailMapper _mapper;

  TimerService(this._database, this._mapper);

  /// Get the currently active timer task (if any)
  Future<TaskDetail?> getActiveTimerTask() async {
    final realm = _database.realm;
    final activeTask = realm
        .query<TaskDetailModel>(
            'taskCommencementTimeStamp != null AND taskStoppedTimeStamp == null')
        .firstOrNull;

    if (activeTask != null) {
      return TaskDetailMapper.toEntity(activeTask);
    }
    return null;
  }

  /// Get task by ID
  Future<TaskDetail?> getTaskById(int taskId) async {
    final realm = _database.realm;
    final task = realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (task != null) {
      return TaskDetailMapper.toEntity(task);
    }
    return null;
  }

  /// Start timer for a task
  Future<void> startTimer(int taskId) async {
    final realm = _database.realm;
    final task = realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (task == null) {
      throw Exception('Task not found');
    }

    // Check if another timer is already running
    final activeTask = realm
        .query<TaskDetailModel>(
            'taskCommencementTimeStamp != null AND taskStoppedTimeStamp == null AND taskId != $taskId')
        .firstOrNull;

    if (activeTask != null) {
      throw Exception(
          'Another timer is already running for task ${activeTask.taskId}');
    }

    realm.write(() {
      task.taskCommencementTimeStamp = DateTime.now();
      task.taskStoppedTimeStamp = null;

      // Clear any existing resume/pause items when starting fresh
      task.resumePauseItems.clear();
    });
  }

  /// Pause timer for a task
  Future<void> pauseTimer(int taskId) async {
    final realm = _database.realm;
    final task = realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (task == null) {
      throw Exception('Task not found');
    }

    if (task.taskCommencementTimeStamp == null ||
        task.taskStoppedTimeStamp != null) {
      throw Exception('Timer is not running');
    }

    // Check if timer is already paused
    final taskEntity = TaskDetailMapper.toEntity(task);
    if (isTimerPaused(taskEntity)) {
      logger('Timer: Task $taskId is already paused, ignoring pause request');
      return;
    }

    realm.write(() {
      if (task.resumePauseItems.isEmpty) {
        // First pause - create new resume/pause item
        final pauseItem = ResumePauseItemModel()
          ..resumeOrderID = '1'
          ..resumeDate = null
          ..pauseDate = DateTime.now();
        task.resumePauseItems.add(pauseItem);
        logger('Timer: First pause for task $taskId - created item with ID 1');
      } else {
        // Check if the last item is a completed pause/resume pair
        final lastItem = _getLastResumeItem(task);
        if (lastItem != null &&
            lastItem.pauseDate != null &&
            lastItem.resumeDate != null) {
          // Last item is complete - create new pause item
          final maxId = _getMaxResumeOrderId(task);
          final pauseItem = ResumePauseItemModel()
            ..resumeOrderID = (maxId + 1).toString()
            ..resumeDate = null
            ..pauseDate = DateTime.now();
          task.resumePauseItems.add(pauseItem);
          logger(
              'Timer: Pause for task $taskId - created new item with ID ${maxId + 1}');
        } else if (lastItem != null && lastItem.pauseDate == null) {
          // Last item is incomplete (no pause date) - update it
          lastItem.pauseDate = DateTime.now();
          logger(
              'Timer: Pause for task $taskId - updated item ${lastItem.resumeOrderID} with pause date');
        } else {
          // Fallback: create new item
          final maxId = _getMaxResumeOrderId(task);
          final pauseItem = ResumePauseItemModel()
            ..resumeOrderID = (maxId + 1).toString()
            ..resumeDate = null
            ..pauseDate = DateTime.now();
          task.resumePauseItems.add(pauseItem);
          logger(
              'Timer: Pause for task $taskId - created fallback item with ID ${maxId + 1}');
        }
      }
      logger(
          'Timer: Task $taskId paused - total items: ${task.resumePauseItems.length}');
    });
  }

  /// Resume timer for a task
  Future<void> resumeTimer(int taskId) async {
    final realm = _database.realm;
    final task = realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (task == null) {
      throw Exception('Task not found');
    }

    if (task.taskCommencementTimeStamp == null ||
        task.taskStoppedTimeStamp != null) {
      throw Exception('Timer is not running');
    }

    // Check if timer is already running (not paused)
    final taskEntity = TaskDetailMapper.toEntity(task);
    if (!isTimerPaused(taskEntity)) {
      logger('Timer: Task $taskId is already running, ignoring resume request');
      return;
    }

    // Check if another timer is already running
    final activeTask = realm
        .query<TaskDetailModel>(
            'taskCommencementTimeStamp != null AND taskStoppedTimeStamp == null AND taskId != $taskId')
        .firstOrNull;

    if (activeTask != null) {
      throw Exception(
          'Another timer is already running for task ${activeTask.taskId}');
    }

    realm.write(() {
      if (task.resumePauseItems.isEmpty) {
        // Error state - should have at least one pause item
        throw Exception('Cannot resume - no pause record found');
      } else {
        // Find the last pause item and update it with resume date
        final lastItem = _getLastResumeItem(task);
        if (lastItem != null &&
            lastItem.pauseDate != null &&
            lastItem.resumeDate == null) {
          // Update the existing pause item with resume date
          lastItem.resumeDate = DateTime.now();
          logger(
              'Timer: Resume for task $taskId - updated item ${lastItem.resumeOrderID} with resume date');
        } else {
          // Fallback: create new item if no proper pause item found
          final maxId = _getMaxResumeOrderId(task);
          final newResumeItem = ResumePauseItemModel()
            ..resumeOrderID = (maxId + 1).toString()
            ..resumeDate = DateTime.now()
            ..pauseDate = null;
          task.resumePauseItems.add(newResumeItem);
          logger(
              'Timer: Resume for task $taskId - created fallback item with ID ${maxId + 1}');
        }
      }
      logger(
          'Timer: Task $taskId resumed - total items: ${task.resumePauseItems.length}');
    });
  }

  /// Stop timer for a task
  Future<void> stopTimer(int taskId) async {
    final realm = _database.realm;
    final task = realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (task == null) {
      throw Exception('Task not found');
    }

    if (task.taskCommencementTimeStamp == null) {
      throw Exception('Timer was not started');
    }

    realm.write(() {
      task.taskStoppedTimeStamp = DateTime.now();
      logger(
          'Timer: Task $taskId stopped - total items: ${task.resumePauseItems.length}');
    });
  }

  /// Check if timer is currently paused
  bool isTimerPaused(TaskDetail task) {
    if (task.resumePauseItems == null || task.resumePauseItems!.isEmpty) {
      return false;
    }

    final lastItem = _getLastResumeItemFromEntity(task);
    if (lastItem == null) return false;

    // Timer is paused if:
    // 1. Last item has pauseDate but no resumeDate (first pause)
    // 2. Last item has both dates, but pauseDate is more recent than resumeDate
    if (lastItem.pauseDate == null) {
      return false; // No pause date means timer is running
    }

    if (lastItem.resumeDate == null) {
      return true; // Has pause date but no resume date - definitely paused
    }

    // Both dates exist - timer is paused if pauseDate is more recent
    return lastItem.pauseDate!.isAfter(lastItem.resumeDate!);
  }

  /// Calculate elapsed time accounting for pauses
  Duration calculateElapsedTime(TaskDetail task) {
    if (task.taskCommencementTimeStamp == null) {
      return Duration.zero;
    }

    final now = DateTime.now();
    final startTime = task.taskCommencementTimeStamp!;

    // Determine the effective end time based on timer state
    DateTime effectiveEndTime;
    if (task.taskStoppedTimeStamp != null) {
      // Timer is stopped - use stop time
      effectiveEndTime = task.taskStoppedTimeStamp!;
    } else if (isTimerPaused(task)) {
      // Timer is paused - use the pause time as end time to freeze elapsed time
      final lastItem = _getLastResumeItemFromEntity(task);
      effectiveEndTime = lastItem?.pauseDate ?? startTime;
    } else {
      // Timer is running - use current time
      effectiveEndTime = now;
    }

    // Calculate total time from start to effective end
    var totalTime = effectiveEndTime.difference(startTime);

    // Subtract pause periods
    final pauseItems = task.resumePauseItems ?? [];
    for (final item in pauseItems) {
      if (item.pauseDate != null) {
        final pauseStart = item.pauseDate!;
        DateTime pauseEnd;

        if (item.resumeDate != null) {
          // Completed pause - use resume date
          pauseEnd = item.resumeDate!;
        } else if (task.taskStoppedTimeStamp != null) {
          // Timer was stopped while paused - use stop time
          pauseEnd = task.taskStoppedTimeStamp!;
        } else if (isTimerPaused(task)) {
          // Currently paused - use pause time (already handled by effectiveEndTime)
          continue;
        } else {
          // Incomplete pause item - skip
          continue;
        }

        final pauseDuration = pauseEnd.difference(pauseStart);
        totalTime = totalTime - pauseDuration;
      }
    }

    return totalTime < Duration.zero ? Duration.zero : totalTime;
  }

  /// Get the last resume/pause item from task model
  ResumePauseItemModel? _getLastResumeItem(TaskDetailModel task) {
    if (task.resumePauseItems.isEmpty) return null;

    int maxId = 0;
    ResumePauseItemModel? lastItem;

    for (final item in task.resumePauseItems) {
      final orderId = int.tryParse(item.resumeOrderID ?? '0') ?? 0;
      if (orderId > maxId) {
        maxId = orderId;
        lastItem = item;
      }
    }

    return lastItem;
  }

  /// Get the last resume/pause item from entity
  ResumePauseItem? _getLastResumeItemFromEntity(TaskDetail task) {
    if (task.resumePauseItems == null || task.resumePauseItems!.isEmpty) {
      return null;
    }

    int maxId = 0;
    ResumePauseItem? lastItem;

    for (final item in task.resumePauseItems!) {
      final orderId = int.tryParse(item.resumeOrderID ?? '0') ?? 0;
      if (orderId > maxId) {
        maxId = orderId;
        lastItem = item;
      }
    }

    return lastItem;
  }

  /// Get the maximum resume order ID
  int _getMaxResumeOrderId(TaskDetailModel task) {
    int maxId = 0;
    for (final item in task.resumePauseItems) {
      final orderId = int.tryParse(item.resumeOrderID ?? '0') ?? 0;
      if (orderId > maxId) {
        maxId = orderId;
      }
    }
    return maxId;
  }
}
