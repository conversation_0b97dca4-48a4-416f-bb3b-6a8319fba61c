import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

/// Service for getting device information
abstract class DeviceInfoService {
  /// Get unique device ID
  Future<String> getDeviceId();
}

/// Implementation of DeviceInfoService
class DeviceInfoServiceImpl implements DeviceInfoService {
  final DeviceInfoPlugin _deviceInfoPlugin;

  DeviceInfoServiceImpl(this._deviceInfoPlugin);

  @override
  Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        // Remove all . from androidInfo.id
        final androidId = androidInfo.id.replaceAll('.', '');
        // Use androidId as primary identifier, fallback to a combination if null
        return androidInfo.id.isNotEmpty
            ? androidId
            : '${androidInfo.brand}_${androidInfo.model}_${androidInfo.fingerprint}'
                .hashCode
                .toString();
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        // Use identifierForVendor as primary identifier, fallback to a combination if null
        return iosInfo.identifierForVendor ??
            '${iosInfo.name}_${iosInfo.model}_${iosInfo.systemVersion}'
                .hashCode
                .toString();
      } else {
        // Fallback for other platforms
        return 'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
      }
    } catch (e) {
      // Fallback in case of any error
      return 'fallback_device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
}
